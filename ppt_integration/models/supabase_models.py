"""
Supabase适配的数据模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
from pydantic import BaseModel, Field
from supabase import create_client, Client
import os

# Supabase配置
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")

class SupabaseClient:
    def __init__(self):
        if not SUPABASE_URL or not SUPABASE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_ANON_KEY must be set")
        self.client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# 数据模型
class PresentationModel(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str
    n_slides: int
    language: str
    title: Optional[str] = None
    file_paths: Optional[List[str]] = None
    outlines: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    layout: Optional[Dict[str, Any]] = None
    structure: Optional[Dict[str, Any]] = None
    instructions: Optional[str] = None
    tone: Optional[str] = None
    verbosity: Optional[str] = None
    include_table_of_contents: bool = False
    include_title_slide: bool = True
    web_search: bool = False
    user_id: Optional[str] = None  # 添加用户ID字段

    def to_supabase_dict(self) -> Dict[str, Any]:
        """转换为Supabase插入格式"""
        return {
            "id": self.id,
            "content": self.content,
            "n_slides": self.n_slides,
            "language": self.language,
            "title": self.title,
            "file_paths": self.file_paths,
            "outlines": self.outlines,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "layout": self.layout,
            "structure": self.structure,
            "instructions": self.instructions,
            "tone": self.tone,
            "verbosity": self.verbosity,
            "include_table_of_contents": self.include_table_of_contents,
            "include_title_slide": self.include_title_slide,
            "web_search": self.web_search,
            "user_id": self.user_id
        }

class SlideModel(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    presentation_id: str
    layout_group: str
    layout: str
    index: int
    content: Dict[str, Any]
    html_content: Optional[str] = None
    speaker_note: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None

    def to_supabase_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "presentation_id": self.presentation_id,
            "layout_group": self.layout_group,
            "layout": self.layout,
            "index": self.index,
            "content": self.content,
            "html_content": self.html_content,
            "speaker_note": self.speaker_note,
            "properties": self.properties
        }

class ImageAsset(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_uploaded: bool = False
    path: str
    extras: Optional[Dict[str, Any]] = None

    def to_supabase_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "created_at": self.created_at.isoformat(),
            "is_uploaded": self.is_uploaded,
            "path": self.path,
            "extras": self.extras
        }

class GeneratePresentationRequest(BaseModel):
    content: str
    slides_markdown: Optional[List[str]] = None
    instructions: Optional[str] = None
    tone: str = "default"
    verbosity: str = "standard"
    web_search: bool = False
    n_slides: int = 8
    language: str = "English"
    template: str = "general"
    include_table_of_contents: bool = False
    include_title_slide: bool = True
    files: Optional[List[str]] = None
    export_as: str = "pptx"
    user_id: Optional[str] = None  # 添加用户ID

# Supabase数据库操作类
class SupabaseRepository:
    def __init__(self):
        self.supabase = SupabaseClient().client

    async def save_presentation(self, presentation: PresentationModel) -> PresentationModel:
        """保存演示文稿到Supabase"""
        try:
            result = self.supabase.table("presentations").insert(
                presentation.to_supabase_dict()
            ).execute()
            return presentation
        except Exception as e:
            raise Exception(f"Failed to save presentation: {str(e)}")

    async def save_slides(self, slides: List[SlideModel]) -> List[SlideModel]:
        """批量保存幻灯片到Supabase"""
        try:
            slides_data = [slide.to_supabase_dict() for slide in slides]
            result = self.supabase.table("slides").insert(slides_data).execute()
            return slides
        except Exception as e:
            raise Exception(f"Failed to save slides: {str(e)}")

    async def save_image_assets(self, assets: List[ImageAsset]) -> List[ImageAsset]:
        """保存图片资源到Supabase"""
        try:
            if not assets:
                return []
            assets_data = [asset.to_supabase_dict() for asset in assets]
            result = self.supabase.table("image_assets").insert(assets_data).execute()
            return assets
        except Exception as e:
            raise Exception(f"Failed to save image assets: {str(e)}")

    async def get_presentation_by_id(self, presentation_id: str) -> Optional[PresentationModel]:
        """根据ID获取演示文稿"""
        try:
            result = self.supabase.table("presentations").select("*").eq("id", presentation_id).execute()
            if result.data:
                return PresentationModel(**result.data[0])
            return None
        except Exception as e:
            raise Exception(f"Failed to get presentation: {str(e)}")

    async def get_slides_by_presentation_id(self, presentation_id: str) -> List[SlideModel]:
        """根据演示文稿ID获取幻灯片"""
        try:
            result = self.supabase.table("slides").select("*").eq("presentation_id", presentation_id).order("index").execute()
            return [SlideModel(**slide) for slide in result.data]
        except Exception as e:
            raise Exception(f"Failed to get slides: {str(e)}")

    async def get_presentations_by_user_id(self, user_id: str) -> List[PresentationModel]:
        """根据用户ID获取演示文稿列表"""
        try:
            result = self.supabase.table("presentations").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            return [PresentationModel(**pres) for pres in result.data]
        except Exception as e:
            raise Exception(f"Failed to get user presentations: {str(e)}")
