"""
资源处理器 - 处理图片和图标生成
"""
import asyncio
from typing import List, Dict, Any, Union

from ..models.supabase_models import SlideModel, ImageAsset
from ..services.image_service import ImageGenerationService, IconFinderService

class AssetProcessor:
    def __init__(self, image_service: ImageGenerationService):
        self.image_service = image_service
        self.icon_service = IconFinderService()

    async def process_slide_assets(self, slide: SlideModel) -> List[ImageAsset]:
        """处理单个幻灯片的资源"""
        generated_assets = []
        
        # 查找所有图片提示
        image_prompts = self._find_image_prompts(slide.content)
        
        # 查找所有图标查询
        icon_queries = self._find_icon_queries(slide.content)
        
        # 创建异步任务
        async_tasks = []
        
        # 为每个图片提示创建生成任务
        for path, prompt in image_prompts:
            async_tasks.append(self._generate_image_for_path(slide, path, prompt))
        
        # 为每个图标查询创建搜索任务
        for path, query in icon_queries:
            async_tasks.append(self._find_icon_for_path(slide, path, query))
        
        # 并发执行所有任务
        if async_tasks:
            results = await asyncio.gather(*async_tasks, return_exceptions=True)
            
            # 收集成功生成的资源
            for result in results:
                if isinstance(result, ImageAsset):
                    generated_assets.append(result)
                elif isinstance(result, Exception):
                    print(f"Asset generation failed: {result}")
        
        return generated_assets

    def _find_image_prompts(self, content: Dict[str, Any], path: str = "") -> List[tuple]:
        """递归查找所有图片提示"""
        prompts = []
        
        for key, value in content.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "__image_prompt__" and isinstance(value, str):
                prompts.append((current_path, value))
            elif isinstance(value, dict):
                prompts.extend(self._find_image_prompts(value, current_path))
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        item_path = f"{current_path}[{i}]"
                        prompts.extend(self._find_image_prompts(item, item_path))
        
        return prompts

    def _find_icon_queries(self, content: Dict[str, Any], path: str = "") -> List[tuple]:
        """递归查找所有图标查询"""
        queries = []
        
        for key, value in content.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "__icon_query__" and isinstance(value, str):
                queries.append((current_path, value))
            elif isinstance(value, dict):
                queries.extend(self._find_icon_queries(value, current_path))
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        item_path = f"{current_path}[{i}]"
                        queries.extend(self._find_icon_queries(item, item_path))
        
        return queries

    async def _generate_image_for_path(self, slide: SlideModel, path: str, prompt: str) -> Union[ImageAsset, None]:
        """为指定路径生成图片"""
        try:
            result = await self.image_service.generate_image(prompt)
            
            if isinstance(result, ImageAsset):
                # 更新幻灯片内容中的图片URL
                self._set_value_at_path(slide.content, path.replace("__image_prompt__", "__image_url__"), result.path)
                return result
            else:
                # 如果返回的是URL字符串，直接设置
                self._set_value_at_path(slide.content, path.replace("__image_prompt__", "__image_url__"), result)
                return None
                
        except Exception as e:
            print(f"Failed to generate image for path {path}: {e}")
            # 设置占位符图片
            self._set_value_at_path(slide.content, path.replace("__image_prompt__", "__image_url__"), "/static/images/placeholder.jpg")
            return None

    async def _find_icon_for_path(self, slide: SlideModel, path: str, query: str) -> Union[ImageAsset, None]:
        """为指定路径查找图标"""
        try:
            icons = await self.icon_service.search_icons(query)
            
            if icons:
                # 使用第一个找到的图标
                icon_url = icons[0]
                self._set_value_at_path(slide.content, path.replace("__icon_query__", "__icon_url__"), icon_url)
            else:
                # 设置占位符图标
                self._set_value_at_path(slide.content, path.replace("__icon_query__", "__icon_url__"), "/static/icons/placeholder.svg")
            
            return None  # 图标通常不需要保存为资源
            
        except Exception as e:
            print(f"Failed to find icon for path {path}: {e}")
            # 设置占位符图标
            self._set_value_at_path(slide.content, path.replace("__icon_query__", "__icon_url__"), "/static/icons/placeholder.svg")
            return None

    def _set_value_at_path(self, obj: Dict[str, Any], path: str, value: Any):
        """在指定路径设置值"""
        parts = path.split('.')
        current = obj
        
        for i, part in enumerate(parts[:-1]):
            # 处理数组索引
            if '[' in part and ']' in part:
                key, index_str = part.split('[')
                index = int(index_str.rstrip(']'))
                
                if key not in current:
                    current[key] = []
                
                # 确保数组有足够的元素
                while len(current[key]) <= index:
                    current[key].append({})
                
                current = current[key][index]
            else:
                if part not in current:
                    current[part] = {}
                current = current[part]
        
        # 设置最终值
        final_key = parts[-1]
        if '[' in final_key and ']' in final_key:
            key, index_str = final_key.split('[')
            index = int(index_str.rstrip(']'))
            
            if key not in current:
                current[key] = []
            
            while len(current[key]) <= index:
                current[key].append({})
            
            current[key][index] = value
        else:
            current[final_key] = value

    def add_placeholder_assets(self, slide: SlideModel):
        """为幻灯片添加占位符资源"""
        # 查找所有图片提示并添加占位符
        image_prompts = self._find_image_prompts(slide.content)
        for path, _ in image_prompts:
            placeholder_path = path.replace("__image_prompt__", "__image_url__")
            self._set_value_at_path(slide.content, placeholder_path, "/static/images/placeholder.jpg")
        
        # 查找所有图标查询并添加占位符
        icon_queries = self._find_icon_queries(slide.content)
        for path, _ in icon_queries:
            placeholder_path = path.replace("__icon_query__", "__icon_url__")
            self._set_value_at_path(slide.content, placeholder_path, "/static/icons/placeholder.svg")

    async def process_slide_assets_with_cache(
        self, 
        slide: SlideModel, 
        old_slide_content: Dict[str, Any] = None
    ) -> List[ImageAsset]:
        """处理幻灯片资源（支持缓存复用）"""
        if old_slide_content is None:
            return await self.process_slide_assets(slide)
        
        generated_assets = []
        
        # 获取新旧内容中的图片提示
        new_image_prompts = dict(self._find_image_prompts(slide.content))
        old_image_prompts = dict(self._find_image_prompts(old_slide_content))
        
        # 获取新旧内容中的图标查询
        new_icon_queries = dict(self._find_icon_queries(slide.content))
        old_icon_queries = dict(self._find_icon_queries(old_slide_content))
        
        # 处理图片
        for path, prompt in new_image_prompts.items():
            if prompt in old_image_prompts.values():
                # 复用旧图片
                old_path = next(p for p, pr in old_image_prompts.items() if pr == prompt)
                old_url_path = old_path.replace("__image_prompt__", "__image_url__")
                old_url = self._get_value_at_path(old_slide_content, old_url_path)
                
                if old_url:
                    new_url_path = path.replace("__image_prompt__", "__image_url__")
                    self._set_value_at_path(slide.content, new_url_path, old_url)
                    continue
            
            # 生成新图片
            asset = await self._generate_image_for_path(slide, path, prompt)
            if asset:
                generated_assets.append(asset)
        
        # 处理图标
        for path, query in new_icon_queries.items():
            if query in old_icon_queries.values():
                # 复用旧图标
                old_path = next(p for p, q in old_icon_queries.items() if q == query)
                old_url_path = old_path.replace("__icon_query__", "__icon_url__")
                old_url = self._get_value_at_path(old_slide_content, old_url_path)
                
                if old_url:
                    new_url_path = path.replace("__icon_query__", "__icon_url__")
                    self._set_value_at_path(slide.content, new_url_path, old_url)
                    continue
            
            # 查找新图标
            await self._find_icon_for_path(slide, path, query)
        
        return generated_assets

    def _get_value_at_path(self, obj: Dict[str, Any], path: str) -> Any:
        """从指定路径获取值"""
        parts = path.split('.')
        current = obj
        
        try:
            for part in parts:
                if '[' in part and ']' in part:
                    key, index_str = part.split('[')
                    index = int(index_str.rstrip(']'))
                    current = current[key][index]
                else:
                    current = current[part]
            return current
        except (KeyError, IndexError, TypeError):
            return None
