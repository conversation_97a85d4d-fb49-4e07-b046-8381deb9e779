"""
布局管理器
"""
from typing import Dict, Any, List
import json

from ..services.llm_service import LLMService, LLMSystemMessage, LLMUserMessage

class LayoutManager:
    def __init__(self):
        self.llm_service = LLMService()
        self.default_layouts = self._get_default_layouts()

    def _get_default_layouts(self) -> Dict[str, Any]:
        """获取默认布局模板"""
        return {
            "general": {
                "name": "general",
                "ordered": False,
                "slides": [
                    {"id": "title", "type": "title", "name": "Title Slide"},
                    {"id": "content", "type": "content", "name": "Content Slide"},
                    {"id": "image", "type": "image", "name": "Image Slide"},
                    {"id": "list", "type": "list", "name": "List Slide"},
                    {"id": "comparison", "type": "comparison", "name": "Comparison Slide"},
                    {"id": "chart", "type": "chart", "name": "Chart Slide"},
                    {"id": "quote", "type": "quote", "name": "Quote Slide"},
                    {"id": "conclusion", "type": "conclusion", "name": "Conclusion Slide"}
                ]
            },
            "business": {
                "name": "business",
                "ordered": False,
                "slides": [
                    {"id": "title", "type": "title", "name": "Title Slide"},
                    {"id": "agenda", "type": "agenda", "name": "Agenda Slide"},
                    {"id": "content", "type": "content", "name": "Content Slide"},
                    {"id": "chart", "type": "chart", "name": "Chart Slide"},
                    {"id": "comparison", "type": "comparison", "name": "Comparison Slide"},
                    {"id": "timeline", "type": "timeline", "name": "Timeline Slide"},
                    {"id": "team", "type": "team", "name": "Team Slide"},
                    {"id": "conclusion", "type": "conclusion", "name": "Conclusion Slide"}
                ]
            },
            "academic": {
                "name": "academic",
                "ordered": False,
                "slides": [
                    {"id": "title", "type": "title", "name": "Title Slide"},
                    {"id": "abstract", "type": "abstract", "name": "Abstract Slide"},
                    {"id": "introduction", "type": "introduction", "name": "Introduction Slide"},
                    {"id": "methodology", "type": "methodology", "name": "Methodology Slide"},
                    {"id": "results", "type": "results", "name": "Results Slide"},
                    {"id": "discussion", "type": "discussion", "name": "Discussion Slide"},
                    {"id": "conclusion", "type": "conclusion", "name": "Conclusion Slide"},
                    {"id": "references", "type": "references", "name": "References Slide"}
                ]
            }
        }

    async def get_layout_by_name(self, template_name: str) -> Dict[str, Any]:
        """根据模板名称获取布局"""
        if template_name in self.default_layouts:
            return self.default_layouts[template_name]
        
        # 如果是自定义模板，可以从数据库加载
        if template_name.startswith("custom-"):
            # 这里可以从Supabase加载自定义模板
            pass
        
        # 默认返回general模板
        return self.default_layouts["general"]

    async def generate_presentation_structure(
        self, 
        outlines: List[Dict[str, Any]], 
        layout_model: Dict[str, Any], 
        instructions: str = None
    ) -> Dict[str, Any]:
        """生成演示文稿结构（为每个幻灯片选择布局）"""
        
        if layout_model.get("ordered", False):
            # 如果是有序布局，直接按顺序分配
            return self._create_ordered_structure(outlines, layout_model)
        
        # 使用LLM为每个幻灯片选择最合适的布局
        return await self._generate_structure_with_llm(outlines, layout_model, instructions)

    def _create_ordered_structure(self, outlines: List[Dict[str, Any]], layout_model: Dict[str, Any]) -> Dict[str, Any]:
        """创建有序结构"""
        slides = []
        layout_slides = layout_model["slides"]
        
        for i, outline in enumerate(outlines):
            layout_index = i % len(layout_slides)
            slides.append(layout_index)
        
        return {"slides": slides}

    async def _generate_structure_with_llm(
        self, 
        outlines: List[Dict[str, Any]], 
        layout_model: Dict[str, Any], 
        instructions: str = None
    ) -> Dict[str, Any]:
        """使用LLM生成结构"""
        
        system_prompt = self._get_structure_system_prompt(layout_model, instructions)
        user_prompt = self._get_structure_user_prompt(outlines)
        
        messages = [
            LLMSystemMessage(content=system_prompt),
            LLMUserMessage(content=user_prompt)
        ]
        
        # 定义响应schema
        response_schema = {
            "type": "object",
            "properties": {
                "slides": {
                    "type": "array",
                    "items": {
                        "type": "integer",
                        "minimum": 0,
                        "maximum": len(layout_model["slides"]) - 1
                    },
                    "minItems": len(outlines),
                    "maxItems": len(outlines)
                }
            },
            "required": ["slides"]
        }
        
        try:
            response = await self.llm_service.generate_structured(messages, response_schema)
            return response
        except Exception as e:
            # 如果LLM生成失败，使用默认策略
            print(f"LLM structure generation failed: {e}, using default strategy")
            return self._create_default_structure(outlines, layout_model)

    def _get_structure_system_prompt(self, layout_model: Dict[str, Any], instructions: str = None) -> str:
        """获取结构生成的系统提示"""
        layout_info = self._format_layout_info(layout_model)
        
        return f"""
You're a professional presentation designer with creative freedom to design engaging presentations.

{layout_info}

# DESIGN PHILOSOPHY
- Create visually compelling and varied presentations
- Match layout to content purpose and audience needs
- Prioritize engagement over rigid formatting rules

{"# User Instructions:" if instructions else ""}
{instructions or ""}

Select layout index for each of the slides based on what will best serve the presentation's goals.
Return a JSON object with a "slides" array containing the layout indices for each slide.
"""

    def _get_structure_user_prompt(self, outlines: List[Dict[str, Any]]) -> str:
        """获取结构生成的用户提示"""
        outline_text = ""
        for i, outline in enumerate(outlines):
            outline_text += f"Slide {i+1}: {outline.get('content', '')[:200]}...\n\n"
        
        return f"""
Please select the most appropriate layout for each slide based on the content:

{outline_text}

Return the layout indices as a JSON array.
"""

    def _format_layout_info(self, layout_model: Dict[str, Any]) -> str:
        """格式化布局信息"""
        layout_info = f"# AVAILABLE LAYOUTS for {layout_model['name']} template:\n\n"
        
        for i, slide in enumerate(layout_model["slides"]):
            layout_info += f"{i}. {slide['name']} ({slide['type']})\n"
        
        return layout_info

    def _create_default_structure(self, outlines: List[Dict[str, Any]], layout_model: Dict[str, Any]) -> Dict[str, Any]:
        """创建默认结构（简单轮询分配）"""
        slides = []
        layout_count = len(layout_model["slides"])
        
        for i in range(len(outlines)):
            # 第一张幻灯片通常是标题页
            if i == 0:
                title_index = self._find_layout_by_type(layout_model, "title")
                slides.append(title_index if title_index != -1 else 0)
            # 最后一张幻灯片通常是结论页
            elif i == len(outlines) - 1:
                conclusion_index = self._find_layout_by_type(layout_model, "conclusion")
                slides.append(conclusion_index if conclusion_index != -1 else (layout_count - 1))
            else:
                # 中间的幻灯片轮询分配
                slides.append((i - 1) % layout_count)
        
        return {"slides": slides}

    def _find_layout_by_type(self, layout_model: Dict[str, Any], layout_type: str) -> int:
        """根据类型查找布局索引"""
        for i, slide in enumerate(layout_model["slides"]):
            if slide.get("type") == layout_type:
                return i
        return -1
