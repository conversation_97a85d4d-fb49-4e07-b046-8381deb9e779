"""
大纲生成器
"""
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
import dirtyjson

from ..services.llm_service import LLMService, LLMSystemMessage, LLMUserMessage

class OutlineGenerator:
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service

    async def generate_outline(
        self,
        content: str,
        n_slides: int,
        language: str,
        tone: Optional[str] = None,
        verbosity: Optional[str] = None,
        instructions: Optional[str] = None,
        include_title_slide: bool = True,
        web_search: bool = False
    ) -> List[Dict[str, Any]]:
        """生成演示文稿大纲"""
        
        system_prompt = self._get_system_prompt(tone, verbosity, instructions, include_title_slide)
        user_prompt = self._get_user_prompt(content, n_slides, language)
        
        messages = [
            LLMSystemMessage(content=system_prompt),
            LLMUserMessage(content=user_prompt)
        ]
        
        # 定义响应schema
        response_schema = self._get_outline_schema(n_slides)
        
        try:
            # 使用流式生成来获取大纲
            outline_text = ""
            async for chunk in self.llm_service.stream_structured(messages, response_schema):
                outline_text += chunk
            
            # 解析JSON响应
            outline_json = dirtyjson.loads(outline_text)
            return outline_json.get("slides", [])
            
        except Exception as e:
            raise Exception(f"Failed to generate outline: {str(e)}")

    def _get_system_prompt(
        self,
        tone: Optional[str] = None,
        verbosity: Optional[str] = None,
        instructions: Optional[str] = None,
        include_title_slide: bool = True
    ) -> str:
        return f"""
You are an expert presentation creator. Generate structured presentations based on user requirements and format them according to the specified JSON schema with markdown content.

{"# User Instruction:" if instructions else ""}
{instructions or ""}

{"# Tone:" if tone else ""}
{tone or ""}

{"# Verbosity:" if verbosity else ""}
{verbosity or ""}

- Provide content for each slide in markdown format.
- Make sure that flow of the presentation is logical and consistent.
- Place greater emphasis on numerical data.
- If Additional Information is provided, divide it into slides.
- Make sure no images are provided in the content.
- Make sure that content follows language guidelines.
- User instruction should always be followed and should supersede any other instruction, except for slide numbers.
- Do not generate table of contents slide.
- Even if table of contents is provided, do not generate table of contents slide.
{"- Always make first slide a title slide." if include_title_slide else "- Do not include title slide in the presentation."}

Return a JSON object with a "slides" array containing slide objects with "content" field.
"""

    def _get_user_prompt(self, content: str, n_slides: int, language: str) -> str:
        return f"""
Generate a presentation with {n_slides} slides in {language} language.

Content: {content}

Please create engaging and informative slides that cover the main topics comprehensively.
"""

    def _get_outline_schema(self, n_slides: int) -> Dict[str, Any]:
        """生成大纲的JSON schema"""
        return {
            "type": "object",
            "properties": {
                "slides": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": "Slide content in markdown format"
                            }
                        },
                        "required": ["content"]
                    },
                    "minItems": n_slides,
                    "maxItems": n_slides
                }
            },
            "required": ["slides"]
        }
