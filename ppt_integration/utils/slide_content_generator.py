"""
幻灯片内容生成器
"""
from typing import Dict, Any, Optional
import json

from ..services.llm_service import LLMService, LLMSystemMessage, LLMUserMessage

class SlideContentGenerator:
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service

    async def generate_slide_content(
        self,
        slide_layout: Dict[str, Any],
        outline: Dict[str, Any],
        language: str,
        tone: Optional[str] = None,
        verbosity: Optional[str] = None,
        instructions: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成单个幻灯片的内容"""
        
        system_prompt = self._get_system_prompt(tone, verbosity, instructions)
        user_prompt = self._get_user_prompt(outline.get("content", ""), language)
        
        messages = [
            LLMSystemMessage(content=system_prompt),
            LLMUserMessage(content=user_prompt)
        ]
        
        # 根据幻灯片布局生成响应schema
        response_schema = self._create_response_schema(slide_layout)
        
        try:
            response = await self.llm_service.generate_structured(messages, response_schema)
            return response
        except Exception as e:
            raise Exception(f"Failed to generate slide content: {str(e)}")

    def _get_system_prompt(
        self,
        tone: Optional[str] = None,
        verbosity: Optional[str] = None,
        instructions: Optional[str] = None
    ) -> str:
        return f"""
Generate structured slide based on provided outline, follow mentioned steps and notes and provide structured output.

{"# User Instructions:" if instructions else ""}
{instructions or ""}

{"# Tone:" if tone else ""}
{tone or ""}

{"# Verbosity:" if verbosity else ""}
{verbosity or ""}

# Steps
1. Analyze the outline.
2. Generate structured slide based on the outline.
3. Generate speaker note that is simple, clear, concise and to the point.

# Notes
- Make sure that content is engaging and informative.
- Use appropriate formatting for the slide type.
- Include relevant details but keep it concise.
- Generate speaker notes that complement the slide content.
- For image prompts, be descriptive and specific.
- For icon queries, use simple, relevant terms.

Return the content in the exact structure specified in the schema.
"""

    def _get_user_prompt(self, outline: str, language: str) -> str:
        return f"""
Generate slide content in {language} language based on this outline:

{outline}

Please create engaging and informative content that matches the slide layout structure.
"""

    def _create_response_schema(self, slide_layout: Dict[str, Any]) -> Dict[str, Any]:
        """根据幻灯片布局创建响应schema"""
        layout_type = slide_layout.get("type", "content")
        
        # 基础schema
        base_schema = {
            "type": "object",
            "properties": {
                "__speaker_note__": {
                    "type": "string",
                    "minLength": 100,
                    "maxLength": 250,
                    "description": "Speaker note for the slide"
                }
            },
            "required": ["__speaker_note__"]
        }
        
        # 根据布局类型添加特定字段
        if layout_type == "title":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Main title of the presentation"
                },
                "subtitle": {
                    "type": "string",
                    "description": "Subtitle or description"
                },
                "author": {
                    "type": "string",
                    "description": "Author or presenter name"
                },
                "__image_prompt__": {
                    "type": "string",
                    "description": "Background image description"
                }
            })
            base_schema["required"].extend(["title", "subtitle"])
            
        elif layout_type == "content":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "content": {
                    "type": "string",
                    "description": "Main content in markdown format"
                },
                "__image_prompt__": {
                    "type": "string",
                    "description": "Supporting image description"
                }
            })
            base_schema["required"].extend(["title", "content"])
            
        elif layout_type == "list":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string"},
                            "__icon_query__": {"type": "string", "description": "Icon search term"}
                        },
                        "required": ["text"]
                    },
                    "minItems": 3,
                    "maxItems": 6
                }
            })
            base_schema["required"].extend(["title", "items"])
            
        elif layout_type == "image":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "description": {
                    "type": "string",
                    "description": "Image description or caption"
                },
                "__image_prompt__": {
                    "type": "string",
                    "description": "Main image description"
                }
            })
            base_schema["required"].extend(["title", "__image_prompt__"])
            
        elif layout_type == "comparison":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "left_side": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string"},
                        "content": {"type": "string"},
                        "__image_prompt__": {"type": "string"}
                    },
                    "required": ["title", "content"]
                },
                "right_side": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string"},
                        "content": {"type": "string"},
                        "__image_prompt__": {"type": "string"}
                    },
                    "required": ["title", "content"]
                }
            })
            base_schema["required"].extend(["title", "left_side", "right_side"])
            
        elif layout_type == "chart":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "chart_type": {
                    "type": "string",
                    "enum": ["bar", "line", "pie", "scatter"],
                    "description": "Type of chart"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "label": {"type": "string"},
                            "value": {"type": "number"}
                        },
                        "required": ["label", "value"]
                    },
                    "minItems": 3,
                    "maxItems": 8
                },
                "description": {
                    "type": "string",
                    "description": "Chart description or insights"
                }
            })
            base_schema["required"].extend(["title", "chart_type", "data"])
            
        elif layout_type == "quote":
            base_schema["properties"].update({
                "quote": {
                    "type": "string",
                    "description": "The main quote text"
                },
                "author": {
                    "type": "string",
                    "description": "Quote author"
                },
                "__image_prompt__": {
                    "type": "string",
                    "description": "Background image for the quote"
                }
            })
            base_schema["required"].extend(["quote", "author"])
            
        elif layout_type == "conclusion":
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Conclusion title"
                },
                "summary": {
                    "type": "string",
                    "description": "Key takeaways summary"
                },
                "call_to_action": {
                    "type": "string",
                    "description": "Call to action or next steps"
                },
                "__image_prompt__": {
                    "type": "string",
                    "description": "Concluding image"
                }
            })
            base_schema["required"].extend(["title", "summary"])
            
        else:
            # 默认内容布局
            base_schema["properties"].update({
                "title": {
                    "type": "string",
                    "description": "Slide title"
                },
                "content": {
                    "type": "string",
                    "description": "Main content"
                }
            })
            base_schema["required"].extend(["title", "content"])
        
        return base_schema
