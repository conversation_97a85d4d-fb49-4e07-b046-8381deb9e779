# PPT生成服务集成指南

这是一个完整的PPT生成服务，可以集成到你的项目中，支持存储到Supabase数据库。

## 功能特性

- 🤖 **多LLM支持**: OpenAI、Google、Anthropic、自定义LLM
- 🎨 **多种布局模板**: 通用、商务、学术等模板
- 🖼️ **图片生成**: DALL-E 3、Gemini、Pixabay、Pexels
- 📊 **结构化内容**: 自动生成标题、列表、图表、对比等
- 🗄️ **Supabase存储**: 完整的数据库集成
- 🔄 **异步处理**: 支持批量和并发处理
- 🎯 **用户隔离**: 支持多用户数据隔离

## 快速开始

### 1. 安装依赖

```bash
pip install supabase openai google-generativeai anthropic aiohttp fastapi uvicorn pydantic
```

### 2. 设置Supabase数据库

1. 在Supabase控制台创建新项目
2. 在SQL编辑器中执行 `database/supabase_schema.sql` 中的SQL语句
3. 获取项目URL和API密钥

### 3. 配置环境变量

```bash
# Supabase配置
export SUPABASE_URL="your-supabase-url"
export SUPABASE_ANON_KEY="your-supabase-anon-key"

# LLM配置（选择一个）
export LLM_PROVIDER="openai"  # openai, google, anthropic, custom
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_MODEL="gpt-4o-mini"

# 图片生成配置（选择一个）
export IMAGE_PROVIDER="dalle3"  # dalle3, gemini, pixabay, pexels
# export PIXABAY_API_KEY="your-pixabay-api-key"  # 如果使用Pixabay
# export PEXELS_API_KEY="your-pexels-api-key"    # 如果使用Pexels
```

### 4. 基本使用

```python
import asyncio
from models.supabase_models import GeneratePresentationRequest
from services.ppt_generator import PPTGenerationService

async def main():
    # 创建服务实例
    ppt_service = PPTGenerationService()
    
    # 创建生成请求
    request = GeneratePresentationRequest(
        content="人工智能在教育领域的应用",
        n_slides=8,
        language="Chinese",
        tone="professional",
        template="general",
        user_id="user-123"
    )
    
    # 生成演示文稿
    result = await ppt_service.generate_presentation(request)
    print(f"生成成功: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 详细配置

### LLM提供商配置

#### OpenAI
```bash
export LLM_PROVIDER="openai"
export OPENAI_API_KEY="sk-..."
export OPENAI_MODEL="gpt-4o-mini"  # 可选，默认gpt-4o-mini
```

#### Google Gemini
```bash
export LLM_PROVIDER="google"
export GOOGLE_API_KEY="your-google-api-key"
export GOOGLE_MODEL="gemini-1.5-flash"  # 可选
```

#### Anthropic Claude
```bash
export LLM_PROVIDER="anthropic"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export ANTHROPIC_MODEL="claude-3-5-sonnet-20241022"  # 可选
```

#### 自定义LLM
```bash
export LLM_PROVIDER="custom"
export CUSTOM_LLM_URL="http://your-llm-endpoint"
export CUSTOM_LLM_API_KEY="your-api-key"  # 可选
export CUSTOM_MODEL="your-model-name"
```

### 图片生成配置

#### DALL-E 3
```bash
export IMAGE_PROVIDER="dalle3"
export OPENAI_API_KEY="sk-..."  # 与LLM共用
```

#### Google Gemini
```bash
export IMAGE_PROVIDER="gemini"
export GOOGLE_API_KEY="your-google-api-key"  # 与LLM共用
```

#### Pixabay
```bash
export IMAGE_PROVIDER="pixabay"
export PIXABAY_API_KEY="your-pixabay-api-key"
```

#### Pexels
```bash
export IMAGE_PROVIDER="pexels"
export PEXELS_API_KEY="your-pexels-api-key"
```

## API集成

### FastAPI集成示例

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from services.ppt_generator import PPTGenerationService
from models.supabase_models import GeneratePresentationRequest

app = FastAPI()

class GenerateRequest(BaseModel):
    content: str
    n_slides: int = 8
    language: str = "English"
    user_id: str

@app.post("/generate-presentation")
async def generate_presentation(request: GenerateRequest):
    try:
        ppt_service = PPTGenerationService()
        
        ppt_request = GeneratePresentationRequest(
            content=request.content,
            n_slides=request.n_slides,
            language=request.language,
            user_id=request.user_id
        )
        
        result = await ppt_service.generate_presentation(ppt_request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/presentations/{user_id}")
async def get_user_presentations(user_id: str):
    ppt_service = PPTGenerationService()
    presentations = await ppt_service.get_user_presentations(user_id)
    return {"presentations": presentations}
```

### 异步任务集成

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncPPTService:
    def __init__(self):
        self.ppt_service = PPTGenerationService()
        self.executor = ThreadPoolExecutor(max_workers=4)

    async def generate_async(self, request_data: dict, callback_url: str = None):
        """异步生成PPT"""
        try:
            request = GeneratePresentationRequest(**request_data)
            result = await self.ppt_service.generate_presentation(request)
            
            # 发送完成通知
            if callback_url:
                await self._send_completion_webhook(callback_url, result)
            
            return result
        except Exception as e:
            # 发送错误通知
            if callback_url:
                await self._send_error_webhook(callback_url, str(e))
            raise

    async def _send_completion_webhook(self, url: str, result: dict):
        """发送完成webhook"""
        # 实现webhook发送逻辑
        pass

    async def _send_error_webhook(self, url: str, error: str):
        """发送错误webhook"""
        # 实现错误webhook发送逻辑
        pass
```

## 自定义扩展

### 添加新的布局模板

```python
# 在 utils/layout_manager.py 中添加新模板
custom_layout = {
    "name": "custom",
    "ordered": False,
    "slides": [
        {"id": "intro", "type": "introduction", "name": "Introduction Slide"},
        {"id": "problem", "type": "problem", "name": "Problem Statement"},
        {"id": "solution", "type": "solution", "name": "Solution Slide"},
        {"id": "demo", "type": "demo", "name": "Demo Slide"},
        {"id": "results", "type": "results", "name": "Results Slide"}
    ]
}
```

### 添加新的内容类型

```python
# 在 utils/slide_content_generator.py 中添加新的内容类型处理
elif layout_type == "demo":
    base_schema["properties"].update({
        "title": {"type": "string"},
        "demo_description": {"type": "string"},
        "steps": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "step_number": {"type": "integer"},
                    "description": {"type": "string"},
                    "__image_prompt__": {"type": "string"}
                }
            }
        }
    })
```

## 故障排除

### 常见问题

1. **Supabase连接失败**
   - 检查SUPABASE_URL和SUPABASE_ANON_KEY是否正确
   - 确认数据库表已正确创建

2. **LLM调用失败**
   - 检查API密钥是否有效
   - 确认模型名称是否正确
   - 检查网络连接

3. **图片生成失败**
   - 检查图片生成服务的API密钥
   - 确认输出目录有写入权限
   - 检查网络连接

### 调试模式

```python
import logging

# 启用调试日志
logging.basicConfig(level=logging.DEBUG)

# 在代码中添加日志
logger = logging.getLogger(__name__)
logger.debug("Debug information")
```

## 性能优化

1. **批量处理**: 使用批量生成减少API调用次数
2. **并发控制**: 合理设置并发数量避免API限制
3. **缓存策略**: 实现图片和内容缓存
4. **资源复用**: 复用相同提示的生成结果

## 安全考虑

1. **API密钥管理**: 使用环境变量或密钥管理服务
2. **用户权限**: 实现适当的用户权限控制
3. **输入验证**: 验证用户输入防止注入攻击
4. **速率限制**: 实现API调用速率限制

## 许可证

MIT License
