-- Supabase数据库表结构
-- 在Supabase控制台的SQL编辑器中执行这些语句

-- 1. 演示文稿表
CREATE TABLE presentations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    n_slides INTEGER NOT NULL,
    language VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    file_paths JSONB,
    outlines JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    layout JSONB,
    structure JSONB,
    instructions TEXT,
    tone VARCHAR(50),
    verbosity VARCHAR(50),
    include_table_of_contents BOOLEAN DEFAULT FALSE,
    include_title_slide BOOLEAN DEFAULT TRUE,
    web_search BOOLEAN DEFAULT FALSE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 2. 幻灯片表
CREATE TABLE slides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    presentation_id UUID REFERENCES presentations(id) ON DELETE CASCADE,
    layout_group VARCHAR(100) NOT NULL,
    layout VARCHAR(100) NOT NULL,
    index INTEGER NOT NULL,
    content JSONB NOT NULL,
    html_content TEXT,
    speaker_note TEXT,
    properties JSONB
);

-- 3. 图片资源表
CREATE TABLE image_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_uploaded BOOLEAN DEFAULT FALSE,
    path TEXT NOT NULL,
    extras JSONB
);

-- 4. 异步任务状态表（可选）
CREATE TABLE async_presentation_tasks (
    id VARCHAR(100) PRIMARY KEY,
    status VARCHAR(50) NOT NULL,
    message TEXT,
    error JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    data JSONB,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 5. 模板表（可选）
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    layout_data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_public BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX idx_presentations_user_id ON presentations(user_id);
CREATE INDEX idx_presentations_created_at ON presentations(created_at DESC);
CREATE INDEX idx_slides_presentation_id ON slides(presentation_id);
CREATE INDEX idx_slides_index ON slides(presentation_id, index);
CREATE INDEX idx_async_tasks_user_id ON async_presentation_tasks(user_id);
CREATE INDEX idx_async_tasks_status ON async_presentation_tasks(status);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_presentations_updated_at 
    BEFORE UPDATE ON presentations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_async_tasks_updated_at 
    BEFORE UPDATE ON async_presentation_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 行级安全策略（RLS）
ALTER TABLE presentations ENABLE ROW LEVEL SECURITY;
ALTER TABLE slides ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE async_presentation_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

-- 演示文稿的RLS策略
CREATE POLICY "Users can view their own presentations" ON presentations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own presentations" ON presentations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own presentations" ON presentations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own presentations" ON presentations
    FOR DELETE USING (auth.uid() = user_id);

-- 幻灯片的RLS策略
CREATE POLICY "Users can view slides of their presentations" ON slides
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM presentations 
            WHERE presentations.id = slides.presentation_id 
            AND presentations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert slides for their presentations" ON slides
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM presentations 
            WHERE presentations.id = slides.presentation_id 
            AND presentations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update slides of their presentations" ON slides
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM presentations 
            WHERE presentations.id = slides.presentation_id 
            AND presentations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete slides of their presentations" ON slides
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM presentations 
            WHERE presentations.id = slides.presentation_id 
            AND presentations.user_id = auth.uid()
        )
    );

-- 图片资源的RLS策略（简化版，实际可能需要更复杂的关联）
CREATE POLICY "Users can view all image assets" ON image_assets
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert image assets" ON image_assets
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 异步任务的RLS策略
CREATE POLICY "Users can view their own tasks" ON async_presentation_tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tasks" ON async_presentation_tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tasks" ON async_presentation_tasks
    FOR UPDATE USING (auth.uid() = user_id);

-- 模板的RLS策略
CREATE POLICY "Everyone can view public templates" ON templates
    FOR SELECT USING (is_public = true OR created_by = auth.uid());

CREATE POLICY "Authenticated users can create templates" ON templates
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = created_by);

CREATE POLICY "Users can update their own templates" ON templates
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own templates" ON templates
    FOR DELETE USING (auth.uid() = created_by);

-- 插入一些默认模板数据
INSERT INTO templates (name, description, layout_data, is_public) VALUES 
('general', 'General purpose presentation template', '{
    "name": "general",
    "ordered": false,
    "slides": [
        {"id": "title", "type": "title", "name": "Title Slide"},
        {"id": "content", "type": "content", "name": "Content Slide"},
        {"id": "image", "type": "image", "name": "Image Slide"},
        {"id": "list", "type": "list", "name": "List Slide"},
        {"id": "comparison", "type": "comparison", "name": "Comparison Slide"}
    ]
}', true),
('business', 'Business presentation template', '{
    "name": "business",
    "ordered": false,
    "slides": [
        {"id": "title", "type": "title", "name": "Title Slide"},
        {"id": "agenda", "type": "agenda", "name": "Agenda Slide"},
        {"id": "content", "type": "content", "name": "Content Slide"},
        {"id": "chart", "type": "chart", "name": "Chart Slide"},
        {"id": "conclusion", "type": "conclusion", "name": "Conclusion Slide"}
    ]
}', true);
