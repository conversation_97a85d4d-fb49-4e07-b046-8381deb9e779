"""
PPT生成服务集成示例
"""
import asyncio
import os
from typing import Optional

from models.supabase_models import GeneratePresentationRequest
from services.ppt_generator import PPTGenerationService

# 环境变量配置示例
def setup_environment():
    """设置环境变量"""
    # Supabase配置
    os.environ["SUPABASE_URL"] = "your-supabase-url"
    os.environ["SUPABASE_ANON_KEY"] = "your-supabase-anon-key"
    
    # LLM配置 - 选择一个提供商
    os.environ["LLM_PROVIDER"] = "openai"  # openai, google, anthropic, custom
    os.environ["OPENAI_API_KEY"] = "your-openai-api-key"
    os.environ["OPENAI_MODEL"] = "gpt-4o-mini"
    
    # 图片生成配置 - 选择一个提供商
    os.environ["IMAGE_PROVIDER"] = "dalle3"  # dalle3, gemini, pixabay, pexels
    # os.environ["PIXABAY_API_KEY"] = "your-pixabay-api-key"  # 如果使用Pixabay
    # os.environ["PEXELS_API_KEY"] = "your-pexels-api-key"    # 如果使用Pexels

class PPTIntegrationExample:
    def __init__(self):
        self.ppt_service = PPTGenerationService()

    async def generate_simple_presentation(self):
        """生成简单演示文稿示例"""
        request = GeneratePresentationRequest(
            content="人工智能在教育领域的应用和发展趋势",
            n_slides=8,
            language="Chinese",
            tone="professional",
            verbosity="standard",
            template="general",
            include_title_slide=True,
            include_table_of_contents=False,
            user_id="user-123"  # 替换为实际用户ID
        )
        
        # 定义进度回调
        async def progress_callback(message: str):
            print(f"进度: {message}")
        
        try:
            result = await self.ppt_service.generate_presentation(
                request, 
                progress_callback=progress_callback
            )
            print(f"演示文稿生成成功: {result}")
            return result
        except Exception as e:
            print(f"生成失败: {e}")
            return None

    async def generate_from_markdown(self):
        """从Markdown生成演示文稿示例"""
        slides_markdown = [
            "# AI在教育中的应用\n\n## 主讲人：张三\n### 2024年演示",
            "# 目录\n\n- AI技术概述\n- 教育应用场景\n- 实施案例\n- 未来展望",
            "# AI技术概述\n\n## 机器学习\n- 监督学习\n- 无监督学习\n- 强化学习\n\n## 深度学习\n- 神经网络\n- 卷积神经网络\n- 循环神经网络",
            "# 教育应用场景\n\n## 个性化学习\n- 学习路径推荐\n- 难度自适应\n- 学习效果评估\n\n## 智能辅导\n- 自动答疑\n- 作业批改\n- 学习建议"
        ]
        
        request = GeneratePresentationRequest(
            content="",  # 使用markdown时content可以为空
            slides_markdown=slides_markdown,
            language="Chinese",
            template="business",
            user_id="user-123"
        )
        
        try:
            result = await self.ppt_service.generate_presentation(request)
            print(f"从Markdown生成成功: {result}")
            return result
        except Exception as e:
            print(f"生成失败: {e}")
            return None

    async def get_user_presentations(self, user_id: str):
        """获取用户演示文稿列表"""
        try:
            presentations = await self.ppt_service.get_user_presentations(user_id)
            print(f"用户 {user_id} 的演示文稿:")
            for pres in presentations:
                print(f"- {pres['title']} ({pres['n_slides']} 张幻灯片)")
            return presentations
        except Exception as e:
            print(f"获取演示文稿列表失败: {e}")
            return []

    async def get_presentation_details(self, presentation_id: str):
        """获取演示文稿详情"""
        try:
            details = await self.ppt_service.get_presentation_with_slides(presentation_id)
            print(f"演示文稿详情:")
            print(f"标题: {details['presentation']['title']}")
            print(f"幻灯片数量: {len(details['slides'])}")
            return details
        except Exception as e:
            print(f"获取演示文稿详情失败: {e}")
            return None

# FastAPI集成示例
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel

app = FastAPI(title="PPT Generation API")

class GenerateRequest(BaseModel):
    content: str
    n_slides: int = 8
    language: str = "English"
    tone: str = "professional"
    verbosity: str = "standard"
    template: str = "general"
    include_title_slide: bool = True
    include_table_of_contents: bool = False
    user_id: str

class GenerateResponse(BaseModel):
    presentation_id: str
    title: str
    slides_count: int
    status: str

@app.post("/generate-presentation", response_model=GenerateResponse)
async def generate_presentation_endpoint(request: GenerateRequest):
    """生成演示文稿API端点"""
    try:
        ppt_service = PPTGenerationService()
        
        ppt_request = GeneratePresentationRequest(
            content=request.content,
            n_slides=request.n_slides,
            language=request.language,
            tone=request.tone,
            verbosity=request.verbosity,
            template=request.template,
            include_title_slide=request.include_title_slide,
            include_table_of_contents=request.include_table_of_contents,
            user_id=request.user_id
        )
        
        result = await ppt_service.generate_presentation(ppt_request)
        
        return GenerateResponse(
            presentation_id=result["presentation_id"],
            title=result["title"],
            slides_count=result["slides_count"],
            status=result["status"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/presentations/{user_id}")
async def get_user_presentations_endpoint(user_id: str):
    """获取用户演示文稿列表"""
    try:
        ppt_service = PPTGenerationService()
        presentations = await ppt_service.get_user_presentations(user_id)
        return {"presentations": presentations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/presentation/{presentation_id}")
async def get_presentation_endpoint(presentation_id: str):
    """获取演示文稿详情"""
    try:
        ppt_service = PPTGenerationService()
        details = await ppt_service.get_presentation_with_slides(presentation_id)
        return details
    except Exception as e:
        raise HTTPException(status_code=404, detail="Presentation not found")

# 异步任务示例（使用Celery或类似的任务队列）
async def generate_presentation_async_task(request_data: dict):
    """异步生成演示文稿任务"""
    try:
        ppt_service = PPTGenerationService()
        request = GeneratePresentationRequest(**request_data)
        
        # 这里可以更新任务状态到数据库
        result = await ppt_service.generate_presentation(request)
        
        # 任务完成后可以发送通知、webhook等
        print(f"异步任务完成: {result}")
        return result
    except Exception as e:
        print(f"异步任务失败: {e}")
        raise

# 主函数示例
async def main():
    """主函数示例"""
    # 设置环境变量
    setup_environment()
    
    # 创建示例实例
    example = PPTIntegrationExample()
    
    # 生成简单演示文稿
    print("=== 生成简单演示文稿 ===")
    result1 = await example.generate_simple_presentation()
    
    if result1:
        # 获取演示文稿详情
        print("\n=== 获取演示文稿详情 ===")
        await example.get_presentation_details(result1["presentation_id"])
    
    # 从Markdown生成
    print("\n=== 从Markdown生成演示文稿 ===")
    result2 = await example.generate_from_markdown()
    
    # 获取用户演示文稿列表
    print("\n=== 获取用户演示文稿列表 ===")
    await example.get_user_presentations("user-123")

if __name__ == "__main__":
    asyncio.run(main())
