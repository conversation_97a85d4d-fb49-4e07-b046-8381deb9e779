"""
PPT生成核心服务
"""
import asyncio
import json
import math
import uuid
from typing import List, Dict, Any, Optional, AsyncGenerator
import dirtyjson

from ..models.supabase_models import (
    PresentationModel, 
    SlideModel, 
    ImageAsset, 
    GeneratePresentationRequest,
    SupabaseRepository
)
from ..services.llm_service import LLMService, LLMSystemMessage, LLMUserMessage
from ..services.image_service import ImageGenerationService
from ..utils.layout_manager import LayoutManager
from ..utils.outline_generator import OutlineGenerator
from ..utils.slide_content_generator import SlideContentGenerator
from ..utils.asset_processor import AssetProcessor

class PPTGenerationService:
    def __init__(self):
        self.llm_service = LLMService()
        self.image_service = ImageGenerationService()
        self.layout_manager = LayoutManager()
        self.outline_generator = OutlineGenerator(self.llm_service)
        self.slide_content_generator = SlideContentGenerator(self.llm_service)
        self.asset_processor = AssetProcessor(self.image_service)
        self.repository = SupabaseRepository()

    async def generate_presentation(
        self, 
        request: GeneratePresentationRequest,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        生成完整的PPT演示文稿
        
        Args:
            request: 生成请求
            progress_callback: 进度回调函数
            
        Returns:
            包含演示文稿ID和路径的字典
        """
        try:
            presentation_id = str(uuid.uuid4())
            
            if progress_callback:
                await progress_callback("开始生成演示文稿大纲...")

            # 1. 生成演示文稿大纲
            if request.slides_markdown:
                # 使用提供的markdown
                outlines = self._create_outlines_from_markdown(request.slides_markdown)
                total_outlines = len(request.slides_markdown)
            else:
                # 使用LLM生成大纲
                n_slides_to_generate = self._calculate_slides_to_generate(request)
                outlines = await self.outline_generator.generate_outline(
                    content=request.content,
                    n_slides=n_slides_to_generate,
                    language=request.language,
                    tone=request.tone,
                    verbosity=request.verbosity,
                    instructions=request.instructions,
                    include_title_slide=request.include_title_slide,
                    web_search=request.web_search
                )
                total_outlines = n_slides_to_generate

            if progress_callback:
                await progress_callback("选择幻灯片布局...")

            # 2. 获取布局和生成结构
            layout_model = await self.layout_manager.get_layout_by_name(request.template)
            presentation_structure = await self.layout_manager.generate_presentation_structure(
                outlines, layout_model, request.instructions
            )

            # 3. 处理目录页
            if request.include_table_of_contents and not request.slides_markdown:
                outlines, presentation_structure = self._inject_table_of_contents(
                    outlines, presentation_structure, request, layout_model
                )

            if progress_callback:
                await progress_callback("生成幻灯片内容...")

            # 4. 创建演示文稿模型
            presentation = PresentationModel(
                id=presentation_id,
                content=request.content,
                n_slides=request.n_slides,
                language=request.language,
                title=self._get_presentation_title(outlines),
                outlines=outlines,
                layout=layout_model,
                structure=presentation_structure,
                tone=request.tone,
                verbosity=request.verbosity,
                instructions=request.instructions,
                include_table_of_contents=request.include_table_of_contents,
                include_title_slide=request.include_title_slide,
                web_search=request.web_search,
                user_id=request.user_id
            )

            # 5. 批量生成幻灯片内容
            slides = await self._generate_slides_batch(
                presentation_structure, layout_model, outlines, 
                presentation_id, request, progress_callback
            )

            if progress_callback:
                await progress_callback("生成图片和图标资源...")

            # 6. 生成资源
            generated_assets = await self._generate_assets_for_slides(slides)

            if progress_callback:
                await progress_callback("保存到数据库...")

            # 7. 保存到Supabase
            await self.repository.save_presentation(presentation)
            await self.repository.save_slides(slides)
            if generated_assets:
                await self.repository.save_image_assets(generated_assets)

            if progress_callback:
                await progress_callback("演示文稿生成完成!")

            return {
                "presentation_id": presentation_id,
                "title": presentation.title,
                "slides_count": len(slides),
                "status": "completed"
            }

        except Exception as e:
            if progress_callback:
                await progress_callback(f"生成失败: {str(e)}")
            raise Exception(f"PPT generation failed: {str(e)}")

    async def _generate_slides_batch(
        self, 
        presentation_structure: Dict[str, Any], 
        layout_model: Dict[str, Any], 
        outlines: List[Dict[str, Any]], 
        presentation_id: str,
        request: GeneratePresentationRequest,
        progress_callback: Optional[callable] = None
    ) -> List[SlideModel]:
        """批量生成幻灯片内容"""
        slides = []
        slide_layout_indices = presentation_structure.get("slides", [])
        slide_layouts = [layout_model["slides"][idx] for idx in slide_layout_indices]
        
        # 分批处理，每批10个
        batch_size = 10
        for start in range(0, len(slide_layouts), batch_size):
            end = min(start + batch_size, len(slide_layouts))
            
            if progress_callback:
                await progress_callback(f"生成幻灯片 {start+1}-{end}...")
            
            # 并发生成这一批的内容
            content_tasks = [
                self.slide_content_generator.generate_slide_content(
                    slide_layouts[i],
                    outlines[i],
                    request.language,
                    request.tone,
                    request.verbosity,
                    request.instructions
                )
                for i in range(start, end)
            ]
            
            batch_contents = await asyncio.gather(*content_tasks)
            
            # 创建幻灯片模型
            for offset, slide_content in enumerate(batch_contents):
                i = start + offset
                slide_layout = slide_layouts[i]
                slide = SlideModel(
                    presentation_id=presentation_id,
                    layout_group=layout_model["name"],
                    layout=slide_layout["id"],
                    index=i,
                    speaker_note=slide_content.get("__speaker_note__"),
                    content=slide_content
                )
                slides.append(slide)
        
        return slides

    async def _generate_assets_for_slides(self, slides: List[SlideModel]) -> List[ImageAsset]:
        """为幻灯片生成图片和图标资源"""
        asset_tasks = []
        
        for slide in slides:
            asset_tasks.append(
                self.asset_processor.process_slide_assets(slide)
            )
        
        # 并发处理所有资源
        generated_assets_lists = await asyncio.gather(*asset_tasks)
        
        # 合并所有资源
        all_assets = []
        for assets_list in generated_assets_lists:
            all_assets.extend(assets_list)
        
        return all_assets

    def _calculate_slides_to_generate(self, request: GeneratePresentationRequest) -> int:
        """计算需要生成的幻灯片数量（考虑目录页）"""
        n_slides_to_generate = request.n_slides
        if request.include_table_of_contents:
            needed_toc_count = math.ceil(
                ((request.n_slides - 1) if request.include_title_slide else request.n_slides) / 10
            )
            n_slides_to_generate -= math.ceil((request.n_slides - needed_toc_count) / 10)
        return n_slides_to_generate

    def _create_outlines_from_markdown(self, slides_markdown: List[str]) -> List[Dict[str, Any]]:
        """从markdown创建大纲"""
        return [{"content": slide} for slide in slides_markdown]

    def _get_presentation_title(self, outlines: List[Dict[str, Any]]) -> str:
        """从大纲提取演示文稿标题"""
        if not outlines:
            return "Untitled Presentation"
        
        first_content = outlines[0].get("content", "")
        return first_content[:100].replace("#", "").replace("/", "").replace("\\", "").replace("\n", " ").strip()

    def _inject_table_of_contents(
        self, 
        outlines: List[Dict[str, Any]], 
        presentation_structure: Dict[str, Any], 
        request: GeneratePresentationRequest,
        layout_model: Dict[str, Any]
    ) -> tuple:
        """注入目录页到演示文稿中"""
        # 这里简化实现，实际需要根据布局模型选择合适的目录页布局
        n_toc_slides = request.n_slides - len(outlines)
        toc_slide_layout_index = 0  # 假设第一个布局是目录页布局
        
        outline_index = 1 if request.include_title_slide else 0
        
        for i in range(n_toc_slides):
            outlines_to = outline_index + 10
            if len(outlines) == outlines_to:
                outlines_to -= 1
            
            # 插入目录页布局索引
            presentation_structure["slides"].insert(
                i + 1 if request.include_title_slide else i,
                toc_slide_layout_index
            )
            
            # 创建目录内容
            toc_content = "Table of Contents\n\n"
            for outline in outlines[outline_index:outlines_to]:
                page_number = outline_index - i + n_toc_slides + (1 if request.include_title_slide else 0)
                toc_content += f"Slide {page_number}: {outline['content'][:100]}\n\n"
                outline_index += 1
            
            # 插入目录大纲
            outlines.insert(
                i + 1 if request.include_title_slide else i,
                {"content": toc_content}
            )
            
            outline_index += 1
        
        return outlines, presentation_structure

    async def get_presentation_with_slides(self, presentation_id: str) -> Dict[str, Any]:
        """获取完整的演示文稿（包含幻灯片）"""
        presentation = await self.repository.get_presentation_by_id(presentation_id)
        if not presentation:
            raise Exception("Presentation not found")
        
        slides = await self.repository.get_slides_by_presentation_id(presentation_id)
        
        return {
            "presentation": presentation.dict(),
            "slides": [slide.dict() for slide in slides]
        }

    async def get_user_presentations(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的演示文稿列表"""
        presentations = await self.repository.get_presentations_by_user_id(user_id)
        return [presentation.dict() for presentation in presentations]
