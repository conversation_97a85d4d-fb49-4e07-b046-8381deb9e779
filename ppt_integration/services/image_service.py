"""
图片生成服务
"""
import os
import uuid
import aiohttp
import asyncio
from typing import Optional, Union
from openai import AsyncOpenAI
import google.generativeai as genai

from ..models.supabase_models import ImageAsset

class ImageGenerationService:
    def __init__(self, output_directory: str = "/tmp/images"):
        self.output_directory = output_directory
        self.provider = self._get_provider()
        self.client = self._get_client()
        
        # 确保输出目录存在
        os.makedirs(output_directory, exist_ok=True)

    def _get_provider(self) -> str:
        """获取图片生成提供商"""
        return os.getenv("IMAGE_PROVIDER", "dalle3").lower()

    def _get_client(self):
        """获取对应的客户端"""
        if self.provider == "dalle3":
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                return None
            return AsyncOpenAI(api_key=api_key)
        elif self.provider == "gemini":
            api_key = os.getenv("GOOGLE_API_KEY")
            if not api_key:
                return None
            genai.configure(api_key=api_key)
            return genai.Client()
        elif self.provider in ["pixabay", "pexels"]:
            # 这些是图片搜索服务，不需要特殊客户端
            return None
        else:
            return None

    async def generate_image(self, prompt: str, theme_prompt: Optional[str] = None) -> Union[str, ImageAsset]:
        """
        生成图片
        
        Args:
            prompt: 图片描述
            theme_prompt: 主题提示（可选）
            
        Returns:
            图片URL或ImageAsset对象
        """
        if not self.client and self.provider not in ["pixabay", "pexels"]:
            print(f"No client available for {self.provider}. Using placeholder image.")
            return "/static/images/placeholder.jpg"

        # 构建完整的提示词
        full_prompt = prompt
        if theme_prompt:
            full_prompt = f"{theme_prompt}, {prompt}"

        try:
            if self.provider == "dalle3":
                return await self._generate_dalle3(full_prompt)
            elif self.provider == "gemini":
                return await self._generate_gemini(full_prompt)
            elif self.provider == "pixabay":
                return await self._search_pixabay(prompt)
            elif self.provider == "pexels":
                return await self._search_pexels(prompt)
            else:
                return "/static/images/placeholder.jpg"
                
        except Exception as e:
            print(f"Error generating image: {e}")
            return "/static/images/placeholder.jpg"

    async def _generate_dalle3(self, prompt: str) -> ImageAsset:
        """使用DALL-E 3生成图片"""
        response = await self.client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            n=1,
            quality="standard",
            size="1024x1024"
        )
        
        image_url = response.data[0].url
        local_path = await self._download_image(image_url)
        
        return ImageAsset(
            path=local_path,
            is_uploaded=False,
            extras={"prompt": prompt, "provider": "dalle3"}
        )

    async def _generate_gemini(self, prompt: str) -> ImageAsset:
        """使用Gemini生成图片"""
        response = await asyncio.to_thread(
            self.client.models.generate_content,
            model="gemini-2.5-flash-image-preview",
            contents=[prompt],
            config=genai.GenerateContentConfig(response_modalities=["TEXT", "IMAGE"])
        )
        
        for part in response.candidates[0].content.parts:
            if part.inline_data is not None:
                image_path = os.path.join(self.output_directory, f"{uuid.uuid4()}.jpg")
                with open(image_path, "wb") as f:
                    f.write(part.inline_data.data)
                
                return ImageAsset(
                    path=image_path,
                    is_uploaded=False,
                    extras={"prompt": prompt, "provider": "gemini"}
                )
        
        raise Exception("No image generated by Gemini")

    async def _search_pixabay(self, query: str) -> str:
        """从Pixabay搜索图片"""
        api_key = os.getenv("PIXABAY_API_KEY")
        if not api_key:
            return "/static/images/placeholder.jpg"
        
        url = "https://pixabay.com/api/"
        params = {
            "key": api_key,
            "q": query,
            "image_type": "photo",
            "orientation": "horizontal",
            "category": "all",
            "min_width": 640,
            "min_height": 480,
            "per_page": 3
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["hits"]:
                        return data["hits"][0]["webformatURL"]
        
        return "/static/images/placeholder.jpg"

    async def _search_pexels(self, query: str) -> str:
        """从Pexels搜索图片"""
        api_key = os.getenv("PEXELS_API_KEY")
        if not api_key:
            return "/static/images/placeholder.jpg"
        
        url = "https://api.pexels.com/v1/search"
        headers = {"Authorization": api_key}
        params = {
            "query": query,
            "per_page": 3,
            "orientation": "landscape"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["photos"]:
                        return data["photos"][0]["src"]["medium"]
        
        return "/static/images/placeholder.jpg"

    async def _download_image(self, url: str) -> str:
        """下载图片到本地"""
        image_path = os.path.join(self.output_directory, f"{uuid.uuid4()}.jpg")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    with open(image_path, "wb") as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    return image_path
        
        raise Exception(f"Failed to download image from {url}")

class IconFinderService:
    """图标查找服务"""
    
    async def search_icons(self, query: str) -> List[str]:
        """搜索图标"""
        # 这里可以集成图标搜索API，比如Iconify、FontAwesome等
        # 简化实现，返回占位符
        return ["/static/icons/placeholder.svg"]
