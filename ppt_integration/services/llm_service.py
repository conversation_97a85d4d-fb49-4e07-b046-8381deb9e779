"""
LLM服务适配器 - 支持多种LLM提供商
"""
import os
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from enum import Enum
import json
from openai import AsyncOpenAI
import google.generativeai as genai
from anthropic import AsyncAnthropic

class LLMProvider(Enum):
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    CUSTOM = "custom"

class LLMMessage:
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

class LLMSystemMessage(LLMMessage):
    def __init__(self, content: str):
        super().__init__("system", content)

class LLMUserMessage(LLMMessage):
    def __init__(self, content: str):
        super().__init__("user", content)

class LLMService:
    def __init__(self):
        self.provider = self._get_provider()
        self.client = self._get_client()
        self.model = self._get_model()

    def _get_provider(self) -> LLMProvider:
        provider_str = os.getenv("LLM_PROVIDER", "openai").lower()
        try:
            return LLMProvider(provider_str)
        except ValueError:
            raise ValueError(f"Unsupported LLM provider: {provider_str}")

    def _get_client(self):
        if self.provider == LLMProvider.OPENAI:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY is required for OpenAI provider")
            return AsyncOpenAI(api_key=api_key)
        
        elif self.provider == LLMProvider.GOOGLE:
            api_key = os.getenv("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY is required for Google provider")
            genai.configure(api_key=api_key)
            return genai.Client()
        
        elif self.provider == LLMProvider.ANTHROPIC:
            api_key = os.getenv("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY is required for Anthropic provider")
            return AsyncAnthropic(api_key=api_key)
        
        elif self.provider == LLMProvider.CUSTOM:
            base_url = os.getenv("CUSTOM_LLM_URL")
            api_key = os.getenv("CUSTOM_LLM_API_KEY", "null")
            if not base_url:
                raise ValueError("CUSTOM_LLM_URL is required for custom provider")
            return AsyncOpenAI(base_url=base_url, api_key=api_key)
        
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

    def _get_model(self) -> str:
        if self.provider == LLMProvider.OPENAI:
            return os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        elif self.provider == LLMProvider.GOOGLE:
            return os.getenv("GOOGLE_MODEL", "gemini-1.5-flash")
        elif self.provider == LLMProvider.ANTHROPIC:
            return os.getenv("ANTHROPIC_MODEL", "claude-3-5-sonnet-20241022")
        elif self.provider == LLMProvider.CUSTOM:
            return os.getenv("CUSTOM_MODEL", "gpt-4o-mini")
        else:
            return "gpt-4o-mini"

    async def generate_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """生成结构化响应"""
        if self.provider == LLMProvider.OPENAI or self.provider == LLMProvider.CUSTOM:
            return await self._generate_openai_structured(messages, response_schema, max_tokens)
        elif self.provider == LLMProvider.GOOGLE:
            return await self._generate_google_structured(messages, response_schema, max_tokens)
        elif self.provider == LLMProvider.ANTHROPIC:
            return await self._generate_anthropic_structured(messages, response_schema, max_tokens)

    async def stream_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> AsyncGenerator[str, None]:
        """流式生成结构化响应"""
        if self.provider == LLMProvider.OPENAI or self.provider == LLMProvider.CUSTOM:
            async for chunk in self._stream_openai_structured(messages, response_schema, max_tokens):
                yield chunk
        else:
            # 对于不支持流式的提供商，回退到普通生成
            result = await self.generate_structured(messages, response_schema, max_tokens)
            yield json.dumps(result)

    async def _generate_openai_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """OpenAI结构化生成"""
        openai_messages = [{"role": msg.role, "content": msg.content} for msg in messages]
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=openai_messages,
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "name": "response",
                    "schema": response_schema
                }
            },
            max_tokens=max_tokens
        )
        
        return json.loads(response.choices[0].message.content)

    async def _stream_openai_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> AsyncGenerator[str, None]:
        """OpenAI流式结构化生成"""
        openai_messages = [{"role": msg.role, "content": msg.content} for msg in messages]
        
        stream = await self.client.chat.completions.create(
            model=self.model,
            messages=openai_messages,
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "name": "response",
                    "schema": response_schema
                }
            },
            max_tokens=max_tokens,
            stream=True
        )
        
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    async def _generate_google_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """Google结构化生成"""
        # 构建Google格式的消息
        system_prompt = None
        user_content = ""
        
        for msg in messages:
            if msg.role == "system":
                system_prompt = msg.content
            else:
                user_content += f"{msg.content}\n"
        
        # 使用asyncio.to_thread来调用同步的Google API
        response = await asyncio.to_thread(
            self.client.models.generate_content,
            model=self.model,
            contents=[user_content],
            config=genai.GenerateContentConfig(
                system_instruction=system_prompt,
                response_mime_type="application/json",
                response_json_schema=response_schema,
                max_output_tokens=max_tokens,
            )
        )
        
        return json.loads(response.candidates[0].content.parts[0].text)

    async def _generate_anthropic_structured(
        self, 
        messages: List[LLMMessage], 
        response_schema: Dict[str, Any],
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """Anthropic结构化生成"""
        system_prompt = None
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_prompt = msg.content
            else:
                anthropic_messages.append({"role": msg.role, "content": msg.content})
        
        # 添加JSON schema指令到系统提示
        if system_prompt:
            system_prompt += f"\n\nPlease respond with valid JSON that matches this schema: {json.dumps(response_schema)}"
        else:
            system_prompt = f"Please respond with valid JSON that matches this schema: {json.dumps(response_schema)}"
        
        response = await self.client.messages.create(
            model=self.model,
            system=system_prompt,
            messages=anthropic_messages,
            max_tokens=max_tokens or 4096
        )
        
        return json.loads(response.content[0].text)

    async def generate_simple(self, messages: List[LLMMessage], max_tokens: Optional[int] = None) -> str:
        """简单文本生成"""
        if self.provider == LLMProvider.OPENAI or self.provider == LLMProvider.CUSTOM:
            openai_messages = [{"role": msg.role, "content": msg.content} for msg in messages]
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        
        elif self.provider == LLMProvider.ANTHROPIC:
            system_prompt = None
            anthropic_messages = []
            
            for msg in messages:
                if msg.role == "system":
                    system_prompt = msg.content
                else:
                    anthropic_messages.append({"role": msg.role, "content": msg.content})
            
            response = await self.client.messages.create(
                model=self.model,
                system=system_prompt,
                messages=anthropic_messages,
                max_tokens=max_tokens or 4096
            )
            return response.content[0].text
        
        # 其他提供商的实现...
        else:
            raise NotImplementedError(f"Simple generation not implemented for {self.provider}")
